#!/usr/bin/env python3
"""
Simple HTTP server to serve the Riva Gleam frontend
This avoids CORS issues when loading local files
"""

import http.server
import socketserver
import os
import sys
import json
import subprocess
from pathlib import Path

# Set the port
PORT = 3001

# Change to the build directory to serve all dependencies
project_dir = Path(__file__).parent
build_dir = project_dir / "build" / "dev" / "javascript"
if build_dir.exists():
    os.chdir(build_dir)
    print(f"📁 Serving from: {build_dir}")
else:
    print(f"❌ Build directory not found: {build_dir}")
    print("💡 Run 'gleam build --target javascript' first")
    sys.exit(1)

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        if self.path == '/api/count':
            self.handle_api_get()
        else:
            super().do_GET()

    def do_POST(self):
        if self.path == '/api/count':
            self.handle_api_post()
        else:
            self.send_error(404)

    def handle_api_get(self):
        """Handle GET /api/count - return current counter value"""
        try:
            # For now, return a simple mock response
            # In a real implementation, we'd call the Gleam database
            response = {"count": 0}

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {"error": f"Server error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())

    def handle_api_post(self):
        """Handle POST /api/count - update counter value"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                action = data.get('action', '')
            else:
                action = ''

            # Mock responses based on action
            if action == 'increment':
                response = {"count": 1}
            elif action == 'decrement':
                response = {"count": 0}
            elif action == 'reset':
                response = {"count": 0}
            else:
                response = {"count": 0}

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {"error": f"Request error: {str(e)}"}
            self.wfile.write(json.dumps(error_response).encode())

def main():
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🌐 Serving Riva Gleam frontend at http://localhost:{PORT}")
            print(f"📁 Serving files from: {project_dir}")
            print(f"🚀 Open your browser to: http://localhost:{PORT}")
            print("📡 API endpoints:")
            print("  - GET  /api/count (get current counter)")
            print("  - POST /api/count (update counter)")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use. Try a different port or stop the existing server.")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
