# Riva Gleam - Justfile
# A modern task runner for Gleam projects with Lustre frontend and Wisp API

# Configuration
port := "3001"
api_port := "8080"
target := "javascript"

# Default recipe
default: help

# Show available recipes
help:
    @echo "🚀 Riva Gleam - Development Commands"
    @echo "===================================="
    @echo ""
    @echo "Quick Start:"
    @echo "  just install    # Install dependencies"
    @echo "  just dev        # Start development server"
    @echo "  just test       # Run tests"
    @echo ""
    @echo "Available commands:"
    @just --list

# Install all dependencies
install: deps
    @echo "✅ Installation complete!"

# Download and install Gleam dependencies
deps:
    @echo "📦 Installing Gleam dependencies..."
    gleam deps download

# Update all dependencies
deps-update:
    @echo "🔄 Updating dependencies..."
    gleam deps update

# Build for JavaScript target (default)
build: build-js

# Build for JavaScript target
build-js:
    @echo "🔨 Building for JavaScript target..."
    gleam build --target javascript
    @echo "✅ JavaScript build complete!"

# Build for Erlang target
build-erl:
    @echo "🔨 Building for Erlang target..."
    gleam build --target erlang
    @echo "✅ Erlang build complete!"

# Build for both targets
build-all: build-js build-erl

# Run all tests
test:
    @echo "🧪 Running tests..."
    gleam test

# Run tests in watch mode (requires entr)
test-watch:
    @echo "👀 Running tests in watch mode..."
    @echo "Press Ctrl+C to stop"
    find src test -name "*.gleam" | entr -c gleam test

# Start the full development environment
dev: build-js
    @echo "🚀 Starting Riva Gleam Development Environment"
    @echo "=============================================="
    @echo ""
    @echo "📋 Development server features:"
    @echo "- ✅ Serves frontend at http://localhost:{{port}}"
    @echo "- ✅ Avoids CORS issues with local files"
    @echo "- ✅ Counter with Increment/Decrement/Reset buttons"
    @echo "- ✅ Loading states for API calls"
    @echo "- ✅ Mock API integration with effects"
    @echo ""
    @echo "🌐 Starting HTTP server on port {{port}}..."
    @echo "Press Ctrl+C to stop the server"
    python3 serve.py

# Start only the frontend development server
dev-frontend: build-js
    @echo "🎨 Starting frontend development server..."
    python3 serve.py

# Start only the API server
dev-api: build-erl
    @echo "🔌 Starting API server..."
    gleam run --target erlang

# Format all Gleam code
format:
    @echo "✨ Formatting code..."
    gleam format

# Check if code is properly formatted
format-check:
    @echo "🔍 Checking code formatting..."
    gleam format --check src test

# Run all code quality checks
check: format-check test

# Generate documentation
docs:
    @echo "📚 Generating documentation..."
    gleam docs build

# Generate and open documentation
docs-open: docs
    @echo "🌐 Opening documentation..."
    #!/usr/bin/env bash
    if command -v xdg-open > /dev/null; then
        xdg-open build/docs/index.html
    elif command -v open > /dev/null; then
        open build/docs/index.html
    else
        echo "⚠️  Please open build/docs/index.html manually"
    fi

# Stop any running development servers
stop:
    @echo "🛑 Stopping development servers..."
    -pkill -f "python3 serve.py" || echo "No Python server running"
    -pkill -f "gleam run" || echo "No Gleam server running"

# Clean build artifacts
clean:
    @echo "🧹 Cleaning build artifacts..."
    rm -rf build/
    @echo "✅ Clean complete!"

# Clean dependencies (forces re-download)
clean-deps:
    @echo "🧹 Cleaning dependencies..."
    rm -rf build/packages/
    gleam deps download

# Show project status
status:
    @echo "📊 Riva Gleam Project Status"
    @echo "============================="
    @echo ""
    @echo "Project Info:"
    @echo "  Name: $(grep '^name' gleam.toml | cut -d'\"' -f2)"
    @echo "  Version: $(grep '^version' gleam.toml | cut -d'\"' -f2)"
    @echo ""
    @echo "Build Status:"
    #!/usr/bin/env bash
    if [ -d "build/dev/javascript" ]; then
        echo "  JavaScript: ✅ Built"
    else
        echo "  JavaScript: ❌ Not built"
    fi
    if [ -d "build/dev/erlang" ]; then
        echo "  Erlang: ✅ Built"
    else
        echo "  Erlang: ❌ Not built"
    fi
    echo ""
    echo "Running Processes:"
    if pgrep -f "python3 serve.py" > /dev/null; then
        echo "  Frontend Server: ✅ Running on port {{port}}"
    else
        echo "  Frontend Server: ❌ Not running"
    fi
    if pgrep -f "gleam run" > /dev/null; then
        echo "  API Server: ✅ Running"
    else
        echo "  API Server: ❌ Not running"
    fi

# Watch for file changes and rebuild (requires entr)
watch:
    @echo "👀 Watching for changes..."
    @echo "Press Ctrl+C to stop"
    find src -name "*.gleam" | entr -c just build-js

# Run CI pipeline
ci: deps format-check test
    @echo "✅ CI pipeline completed successfully!"

# Prepare a release build
release: clean ci build-all docs
    @echo "🎉 Release build completed!"

# Development shortcuts
alias js := build-js
alias erl := build-erl
alias t := test
alias f := format
alias d := dev
alias s := status
