import api_server
import database
import gleam/int
import gleam/io

// Test API functionality by testing database operations directly

fn test_get_count() {
  io.println("Testing GET /api/count...")

  case database.init() {
    Ok(db) -> {
      case database.get_counter(db) {
        Ok(count) -> {
          io.println("✅ GET /api/count returned: " <> int.to_string(count))
          let _ = database.close(db)
          Nil
        }
        Error(err) -> {
          case err {
            database.ConnectionError(msg) ->
              io.println("❌ Connection error: " <> msg)
            database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
          }
          let _ = database.close(db)
          Nil
        }
      }
    }
    Error(err) -> {
      case err {
        database.ConnectionError(msg) ->
          io.println("❌ Connection error: " <> msg)
        database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
      }
    }
  }
}

fn test_update_count() {
  io.println("Testing POST /api/count...")

  case database.init() {
    Ok(db) -> {
      // Test increment
      case database.increment_counter(db) {
        Ok(count) -> {
          io.println(
            "✅ POST /api/count (increment) returned: " <> int.to_string(count),
          )

          // Test decrement
          case database.decrement_counter(db) {
            Ok(count2) -> {
              io.println(
                "✅ POST /api/count (decrement) returned: "
                <> int.to_string(count2),
              )

              // Test reset
              case database.reset_counter(db) {
                Ok(count3) -> {
                  io.println(
                    "✅ POST /api/count (reset) returned: "
                    <> int.to_string(count3),
                  )
                  let _ = database.close(db)
                  Nil
                }
                Error(err) -> {
                  case err {
                    database.ConnectionError(msg) ->
                      io.println("❌ Connection error: " <> msg)
                    database.QueryError(msg) ->
                      io.println("❌ Query error: " <> msg)
                  }
                  let _ = database.close(db)
                  Nil
                }
              }
            }
            Error(err) -> {
              case err {
                database.ConnectionError(msg) ->
                  io.println("❌ Connection error: " <> msg)
                database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
              }
              let _ = database.close(db)
              Nil
            }
          }
        }
        Error(err) -> {
          case err {
            database.ConnectionError(msg) ->
              io.println("❌ Connection error: " <> msg)
            database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
          }
          let _ = database.close(db)
          Nil
        }
      }
    }
    Error(err) -> {
      case err {
        database.ConnectionError(msg) ->
          io.println("❌ Connection error: " <> msg)
        database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
      }
    }
  }
}

pub fn main() {
  io.println("Testing API with database integration...")

  // Test server initialization
  case api_server.start_server() {
    Ok(_) -> {
      io.println("✅ API server initialized successfully")

      // Test API endpoints
      test_get_count()
      test_update_count()

      io.println("✅ All API tests completed!")
    }
    Error(msg) -> {
      io.println("❌ Failed to initialize API server: " <> msg)
    }
  }
}
