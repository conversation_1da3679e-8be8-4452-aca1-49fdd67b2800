import gleam/dynamic/decode
import gleam/int
import sqlight

pub type DatabaseError {
  ConnectionError(String)
  QueryError(String)
}

pub type Database {
  Database(connection: sqlight.Connection)
}

fn error_to_string(error: sqlight.Error) -> String {
  case error {
    sqlight.SqlightError(code: _, message: msg, offset: _) -> msg
  }
}

/// Initialize the database and create the counter table if it doesn't exist
pub fn init() -> Result(Database, DatabaseError) {
  case sqlight.open("counter.db") {
    Ok(conn) -> {
      let create_table_sql =
        "
        CREATE TABLE IF NOT EXISTS counter (
          id INTEGER PRIMARY KEY CHECK (id = 1),
          value INTEGER NOT NULL DEFAULT 0
        )
      "

      case sqlight.exec(create_table_sql, on: conn) {
        Ok(_) -> {
          // Insert initial value if table is empty
          let insert_initial_sql =
            "
            INSERT OR IGNORE INTO counter (id, value) VALUES (1, 0)
          "
          case sqlight.exec(insert_initial_sql, on: conn) {
            Ok(_) -> Ok(Database(conn))
            Error(err) ->
              <PERSON>rror(QueryError(
                "Failed to insert initial value: " <> error_to_string(err),
              ))
          }
        }
        Error(err) ->
          Error(QueryError("Failed to create table: " <> error_to_string(err)))
      }
    }
    Error(err) ->
      Error(ConnectionError("Failed to open database: " <> error_to_string(err)))
  }
}

/// Get the current counter value from the database
pub fn get_counter(db: Database) -> Result(Int, DatabaseError) {
  let sql = "SELECT value FROM counter WHERE id = 1"
  let decoder = {
    use value <- decode.field(0, decode.int)
    decode.success(value)
  }

  case sqlight.query(sql, on: db.connection, with: [], expecting: decoder) {
    Ok([value]) -> Ok(value)
    Ok([]) -> Error(QueryError("Counter not found"))
    Ok(_) -> Error(QueryError("Multiple counter values found"))
    Error(err) ->
      Error(QueryError("Failed to get counter: " <> error_to_string(err)))
  }
}

/// Update the counter value in the database
pub fn set_counter(db: Database, value: Int) -> Result(Int, DatabaseError) {
  let sql =
    "UPDATE counter SET value = " <> int.to_string(value) <> " WHERE id = 1"

  case sqlight.exec(sql, on: db.connection) {
    Ok(_) -> Ok(value)
    Error(err) ->
      Error(QueryError("Failed to update counter: " <> error_to_string(err)))
  }
}

/// Increment the counter by 1
pub fn increment_counter(db: Database) -> Result(Int, DatabaseError) {
  case get_counter(db) {
    Ok(current) -> set_counter(db, current + 1)
    Error(err) -> Error(err)
  }
}

/// Decrement the counter by 1
pub fn decrement_counter(db: Database) -> Result(Int, DatabaseError) {
  case get_counter(db) {
    Ok(current) -> set_counter(db, current - 1)
    Error(err) -> Error(err)
  }
}

/// Reset the counter to 0
pub fn reset_counter(db: Database) -> Result(Int, DatabaseError) {
  set_counter(db, 0)
}

/// Close the database connection
pub fn close(db: Database) -> Result(Nil, DatabaseError) {
  case sqlight.close(db.connection) {
    Ok(_) -> Ok(Nil)
    Error(err) ->
      Error(ConnectionError(
        "Failed to close database: " <> error_to_string(err),
      ))
  }
}
