name = "riva_gleam"
version = "1.0.0"
description = "A modern web application built with Gleam, <PERSON><PERSON><PERSON>, and Wisp"
licences = ["Apache-2.0"]

# Project metadata
# repository = { type = "github", user = "your-username", repo = "riva_gleam" }
# links = [{ title = "Website", href = "https://your-domain.com" }]

# Build configuration
target = "javascript"

# For a full reference of all the available options, you can have a look at
# https://gleam.run/writing-gleam/gleam-toml/.

[dependencies]
gleam_stdlib = ">= 0.44.0 and < 2.0.0"
lustre = ">= 5.2.1 and < 6.0.0"
wisp = ">= 1.8.0 and < 2.0.0"
gleam_http = ">= 4.0.0 and < 5.0.0"
gleam_json = ">= 3.0.1 and < 4.0.0"
gleam_fetch = ">= 1.3.0 and < 2.0.0"
gleam_erlang = ">= 1.1.0 and < 2.0.0"
sqlight = ">= 1.0.1 and < 2.0.0"
gleam_javascript = ">= 1.0.0 and < 2.0.0"

[dev-dependencies]
gleeunit = ">= 1.0.0 and < 2.0.0"
